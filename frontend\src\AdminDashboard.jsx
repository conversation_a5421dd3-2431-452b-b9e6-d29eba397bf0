import { useNavigate } from "react-router-dom";
import { useState } from "react";
import axios from "axios";

export default function AdminDashboard() {
  const nav = useNavigate();
  const [processing, setProcessing] = useState(false);

  const runPipeline = async () => {
    if (!window.confirm("Have ALL uploads finished? This will start the processing pipeline.")) return;
    setProcessing(true);
    try {
      const token = localStorage.getItem("access");
      await axios.post(
        "/api/tickets/process_uploads/",
        {},
        {
          headers: { Authorization: `Bearer ${token}` },
        }
      );
      alert("Pipeline Completed");
    } catch (err) {
      alert(err.response?.data?.detail || "Error starting process.");
    } finally {
      setProcessing(false);
    }
  };

  const handleLogout = () => {
    localStorage.removeItem("userData");
    localStorage.removeItem("access");
    localStorage.removeItem("refresh");

    nav("/auth", { replace: true });

    setTimeout(() => {
      if (window.location.pathname !== "/auth") {
        window.location.href = "/auth";
      }
    }, 300);
  };

  return (
    <div
      style={{
        minHeight: "100vh",
        background: "linear-gradient(to bottom right, #1E3A8A, #3B82F6)",
        display: "flex",
        justifyContent: "center",
        alignItems: "center",
        padding: "2rem",
      }}
    >
      <div
        style={{
          background: "rgba(255, 255, 255, 0.95)",
          borderRadius: "1.5rem",
          padding: "32px",
          width: "100%",
          maxWidth: "480px",
          boxShadow: "0 20px 25px rgba(0, 0, 0, 0.25)",
          backdropFilter: "blur(10px)",
        }}
      >
        <h2
          style={{
            fontSize: "2rem",
            fontWeight: "600",
            color: "#1E3A8A",
            textAlign: "center",
            marginBottom: "24px",
            borderBottom: "2px solid #E5E7EB",
            paddingBottom: "12px",
          }}
        >
          Admin Dashboard
        </h2>

        <div style={{ display: "flex", flexDirection: "column", gap: "16px" }}>
          <DashboardButton
            label="Uploads"
            color="#2563EB"
            hover="#1E40AF"
            onClick={() => nav("/uploads")}
          />
          <DashboardButton
            label="Prompt Templates"
            color="#3B82F6"
            hover="#2563EB"
            onClick={() => nav("/prompt-manager")}
          />
          <DashboardButton
            label="Escalated Tickets"
            color="#9CA3AF"
            hover="#6B7280"
            onClick={() => nav("/admin/tickets")}
            disabled
          />
          <DashboardButton
            label="Admin Chatbot"
            color="#1E3A8A"
            hover="#1E40AF"
            onClick={() => nav("/admin/chatbot")}
          />
          <DashboardButton
            label="Usage Dashboard"
            color="#3B82F6"
            hover="#2563EB"
            onClick={() => nav("/usage")}
          />
          <DashboardButton
            label={processing ? "Processing..." : "Process Uploads"}
            color={processing ? "#9CA3AF" : "#EF4444"}
            hover={processing ? "#9CA3AF" : "#DC2626"}
            onClick={runPipeline}
            disabled={processing}
          />
          <DashboardButton
            label="Logout"
            color="#6B7280"
            hover="#4B5563"
            onClick={handleLogout}
          />
        </div>
      </div>
    </div>
  );
}

function DashboardButton({ label, color, hover, onClick, disabled = false }) {
  const [isHovered, setHovered] = useState(false);

  return (
    <button
      onClick={onClick}
      disabled={disabled}
      onMouseEnter={() => setHovered(true)}
      onMouseLeave={() => setHovered(false)}
      style={{
        padding: "14px 20px",
        borderRadius: "0.75rem",
        backgroundColor: isHovered && !disabled ? hover : color,
        color: "#ffffff",
        fontSize: "1rem",
        fontWeight: 600,
        border: "none",
        cursor: disabled ? "not-allowed" : "pointer",
        transition: "all 0.2s ease-in-out",
        boxShadow: disabled ? "none" : isHovered ? "0 6px 12px rgba(0, 0, 0, 0.15)" : "0 4px 6px rgba(0, 0, 0, 0.1)",
        transform: disabled ? "none" : isHovered ? "translateY(-1px)" : "translateY(0)",
      }}
    >
      {label}
    </button>
  );
}
