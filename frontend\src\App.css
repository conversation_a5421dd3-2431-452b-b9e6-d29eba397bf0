body {
  font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', 'Roboto', sans-serif;
  margin: 0;
  padding: 0;
  background: #1d656d;
  background-color: #1d656d;
  height: 100vh;
  overflow: hidden;
}

/* Global Blue Theme Styles */
.blue-gradient-bg {
  background: linear-gradient(to bottom right, #1E3A8A, #3B82F6);
  min-height: 100vh;
}

.blue-card {
  background: rgba(255, 255, 255, 0.95);
  border-radius: 1rem;
  box-shadow: 0 10px 25px rgba(0, 0, 0, 0.1);
  backdrop-filter: blur(10px);
}

.blue-button {
  background: #2563EB;
  color: white;
  border: none;
  padding: 0.75rem 1.5rem;
  border-radius: 0.75rem;
  font-weight: 600;
  cursor: pointer;
  transition: all 0.2s ease-in-out;
  box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
}

.blue-button:hover {
  background: #1E40AF;
  transform: translateY(-1px);
  box-shadow: 0 6px 12px rgba(0, 0, 0, 0.15);
}

.blue-button:disabled {
  background: #9CA3AF;
  cursor: not-allowed;
  transform: none;
}

.blue-input {
  width: 100%;
  padding: 0.75rem;
  border: 2px solid #E5E7EB;
  border-radius: 0.75rem;
  font-size: 1rem;
  transition: border-color 0.2s ease-in-out;
  background: white;
}

.blue-input:focus {
  outline: none;
  border-color: #3B82F6;
  box-shadow: 0 0 0 3px rgba(59, 130, 246, 0.1);
}

.blue-heading {
  font-size: 2rem;
  font-weight: 600;
  color: white;
  text-align: center;
  margin-bottom: 1.5rem;
}

.blue-text {
  color: #DBEAFE;
  font-size: 1rem;
  line-height: 1.6;
}

.blue-container {
  display: flex;
  justify-content: center;
  align-items: center;
  min-height: 100vh;
  padding: 2rem;
}

.blue-modal {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(30, 58, 138, 0.8);
  display: flex;
  justify-content: center;
  align-items: center;
  z-index: 1000;
  backdrop-filter: blur(4px);
}

.blue-modal-content {
  background: white;
  border-radius: 1rem;
  padding: 2rem;
  max-width: 90vw;
  max-height: 80vh;
  overflow-y: auto;
  box-shadow: 0 20px 25px rgba(0, 0, 0, 0.25);
}

.chat-container {
  display: flex;
  flex-direction: column;
  height: 100vh;
  max-width: 800px;
  margin: 0 auto;
  background-color: #1d656d; /* Updated to match background */
  box-shadow: 0 0 20px rgba(0,0,0,0.1);
}

.chat-header {
  background: linear-gradient(135deg, #1d656d 0%, #104f52 100%);
  color: white;
  padding: 20px;
  text-align: center;
  border-bottom: 1px solid #0d3f41;
}

.chat-header h1 {
  margin: 0 0 5px 0;
  font-size: 24px;
  font-weight: 600;
}

.chat-header .subtitle {
  margin: 0;
  opacity: 0.9;
  font-size: 14px;
}

.chat-messages {
  flex: 1;
  overflow-y: auto;
  padding: 20px;
  background-color: #f8f9fa;
  display: flex;
  flex-direction: column;
  gap: 15px;
}

.message {
  display: flex;
  margin-bottom: 15px;
}

.message.user {
  justify-content: flex-end;
}

.message.bot {
  justify-content: flex-start;
}

.message-content {
  max-width: 70%;
  position: relative;
}

.message.user .message-content {
  background: linear-gradient(135deg, #1d656d 0%, #104f52 100%);
  color: white;
  border-radius: 18px 18px 4px 18px;
  padding: 12px 16px;
}


.message.bot .message-content {
  background: white;
  color: #333;
  border-radius: 18px 18px 18px 4px;
  padding: 12px 16px;
  box-shadow: 0 1px 2px rgba(0,0,0,0.1);
}

.message-text {
  line-height: 1.4;
  word-wrap: break-word;
}

.message-time {
  font-size: 11px;
  opacity: 0.7;
  margin-top: 4px;
  text-align: right;
}

.message.bot .message-time {
  text-align: left;
}

.typing-indicator {
  display: flex;
  gap: 4px;
  margin-bottom: 8px;
}

.typing-indicator span {
  width: 8px;
  height: 8px;
  border-radius: 50%;
  background-color: #999;
  animation: typing 1.4s infinite ease-in-out;
}

.typing-indicator span:nth-child(1) { animation-delay: -0.32s; }
.typing-indicator span:nth-child(2) { animation-delay: -0.16s; }

@keyframes typing {
  0%, 80%, 100% { transform: scale(0.8); opacity: 0.5; }
  40% { transform: scale(1); opacity: 1; }
}

.message.typing .message-text {
  font-style: italic;
  opacity: 0.8;
}

.chat-input-form {
  padding: 20px;
  background: white;
  border-top: 1px solid #e0e0e0;
}

.input-container {
  display: flex;
  gap: 10px;
  align-items: center;
}

.input-container input {
  flex: 1;
  padding: 12px 16px;
  border: 2px solid #e0e0e0;
  border-radius: 25px;
  font-size: 16px;
  outline: none;
  transition: border-color 0.3s ease;
}

.input-container input:focus {
  border-color: #667eea;
}

.input-container input:disabled {
  background-color: #f5f5f5;
  cursor: not-allowed;
}

.input-container button {
  width: 45px;
  height: 45px;
  border: none;
  border-radius: 50%;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  color: white;
  font-size: 18px;
  cursor: pointer;
  transition: transform 0.2s ease;
  display: flex;
  align-items: center;
  justify-content: center;
}

.input-container button:hover:not(:disabled) {
  transform: scale(1.05);
}

.input-container button:disabled {
  background: #ccc;
  cursor: not-allowed;
  transform: none;
}

.error-message {
  background-color: #fee;
  color: #c33;
  padding: 12px 20px;
  margin: 10px 20px;
  border-radius: 8px;
  border-left: 4px solid #c33;
  font-size: 14px;
}

.chat-messages::-webkit-scrollbar {
  width: 6px;
}

.chat-messages::-webkit-scrollbar-track {
  background: #f1f1f1;
}

.chat-messages::-webkit-scrollbar-thumb {
  background: #c1c1c1;
  border-radius: 3px;
}

.chat-messages::-webkit-scrollbar-thumb:hover {
  background: #a1a1a1;
}

@media (max-width: 768px) {
  .chat-container {
    height: 100vh;
    border-radius: 0;
  }

  .message-content {
    max-width: 85%;
  }

  .chat-header {
    padding: 15px;
  }

  .chat-messages {
    padding: 15px;
  }

  .chat-input-form {
    padding: 15px;
  }
}

.typing {
  display: flex;
  align-items: center;
  gap: 8px;
  color: #555;
  font-size: 0.9rem;
  padding: 10px;
}

.dot {
  width: 8px;
  height: 8px;
  border-radius: 50%;
  animation: pulse 1.4s infinite ease-in-out;
}

.dot:nth-child(2) {
  animation-delay: 0.2s;
}

.dot:nth-child(3) {
  animation-delay: 0.4s;
}

@keyframes pulse {
  0%, 100% {
    opacity: 0.4;
  }
  50% {
    opacity: 1;
  }
}

.chat-input-form {
  display: flex;
  align-items: center;
  gap: 12px;
  padding: 15px 20px;
  background-color: #fff;
  border-top: 1px solid #e0e0e0;
  box-shadow: 0 -2px 5px rgba(0, 0, 0, 0.05);
}

.chat-input-form textarea {
  flex: 1;
  padding: 12px 16px;
  border: 1px solid #d1d1d1;
  border-radius: 12px;
  font-size: 1rem;
  line-height: 1.4;
  resize: none;
  background-color: #fafafa;
  transition: border-color 0.2s ease, box-shadow 0.2s ease;
}

.chat-input-form textarea:focus {
  outline: none;
  border-color: #007bff;
  box-shadow: 0 0 0 3px rgba(0, 123, 255, 0.1);
}

.chat-input-form textarea::placeholder {
  color: #888;
  font-style: italic;
}

.chat-input-form textarea:disabled {
  background-color: #e9ecef;
  cursor: not-allowed;
}

.chat-input-form button {
  padding: 12px 24px;
  background-color: #007bff;
  color: #fff;
  border: none;
  border-radius: 12px;
  font-size: 1rem;
  font-weight: 500;
  cursor: pointer;
  transition: background-color 0.2s ease, transform 0.1s ease;
}

.chat-input-form button:hover:not(:disabled) {
  background-color: #0056b3;
  transform: translateY(-1px);
}

.chat-input-form button:active:not(:disabled) {
  transform: translateY(0);
}

.chat-input-form button:disabled {
  background-color: #6c757d;
  cursor: not-allowed;
}

/* Smooth scrolling for all scrollable elements */
* {
  scroll-behavior: smooth;
}

/* Custom scrollbar styling */
::-webkit-scrollbar {
  width: 8px;
}

::-webkit-scrollbar-track {
  background: #f1f1f1;
  border-radius: 4px;
}

::-webkit-scrollbar-thumb {
  background: #c1c1c1;
  border-radius: 4px;
}

::-webkit-scrollbar-thumb:hover {
  background: #a8a8a8;
}

/* Scrollable containers */
.scrollable {
  max-height: 60vh;
  overflow-y: auto;
  scroll-behavior: smooth;
}

/* Animation for loading spinner */
@keyframes spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}
